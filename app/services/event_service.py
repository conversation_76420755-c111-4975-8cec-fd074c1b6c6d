"""
Event service for managing event processing operations.
"""

from typing import Dict, List, Optional, Any
from uuid import uuid4

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.repositories.event_repository import EventRepository
from app.repositories.event_type_repository import EventTypeRepository
from app.repositories.workflow_repository import WorkFlowRepository
from app.schemas.event_type import EventCreate, EventStatus, Event
from app.services.validation_service import ValidationService
from app.services.temporal_service import TemporalService
from app.services.workflow_service import WorkflowService
from app.utils.exceptions import NotFoundError, ValidationError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger("services.event")


class EventService:
    """Service for event processing operations."""
    
    def __init__(
        self, 
        mongodb: AsyncIOMotorDatabase,
        workflow_service: Optional[WorkflowService] = None,
        temporal_service: Optional[TemporalService] = None
    ):
        """
        Initialize event service.
        
        Args:
            mongodb: MongoDB database instance
            workflow_service: Optional workflow service instance
            temporal_service: Optional temporal service instance
        """
        self.event_repository = EventRepository(mongodb)
        self.event_type_repository = EventTypeRepository(mongodb)
        self.validation_service = ValidationService()
        self.workflow_service = workflow_service
        self.temporal_service = temporal_service
        
        # Ensure indexes are created
        self._ensure_indexes_task = None
    
    async def ensure_indexes(self) -> None:
        """Ensure database indexes are created."""
        if self._ensure_indexes_task is None:
            self._ensure_indexes_task = True
            await self.event_repository.ensure_indexes()
    
    async def create_event(self, event_data: EventCreate) -> Dict[str, Any]:
        """
        Create a new event with initial status.
        
        Args:
            event_data: Event creation data
            
        Returns:
            Dict[str, Any]: Created event
            
        Raises:
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating event of type: {event_data.event_type}")
        
        # Ensure indexes exist
        await self.ensure_indexes()
        
        # Create the event
        created_event = await self.event_repository.create(event_data)
        
        logger.info(f"Successfully created event: {created_event['id']}")
        return created_event
    
    async def validate_event_data(self, event_id: str) -> Dict[str, Any]:
        """
        Validate event data against its event type schema.
        
        Args:
            event_id: Event ID
            
        Returns:
            Dict[str, Any]: Updated event with validation results
            
        Raises:
            NotFoundError: If event or event type not found
            DatabaseError: If database operation fails
        """
        logger.info(f"Validating event data for event: {event_id}")
        
        # Get the event
        event = await self.event_repository.get_by_id(event_id)
        if not event:
            raise NotFoundError(f"Event with ID {event_id} not found")
        
        # Get the event type
        event_type = await self.event_type_repository.get_by_name(event["event_type"])
        if not event_type:
            raise NotFoundError(f"Event type '{event['event_type']}' not found")
        
        try:
            # Validate the event data against the schema
            validation_result = self.validation_service.validate_data_against_schema(
                event["data"], 
                event_type["schema"]
            )
            
            if validation_result.valid:
                # Validation successful
                updated_event = await self.event_repository.update_status(
                    event_id, 
                    EventStatus.IN_PROGRESS  # Keep in progress for workflow execution
                )
                logger.info(f"Event {event_id} validation successful")
                return updated_event or event
            else:
                # Validation failed
                error_messages = [error.message for error in validation_result.errors]
                updated_event = await self.event_repository.update_status(
                    event_id, 
                    EventStatus.VALIDATION_FAILED,
                    validation_errors=error_messages
                )
                logger.warning(f"Event {event_id} validation failed: {error_messages}")
                return updated_event or event
                
        except Exception as e:
            logger.error(f"Error during event validation: {e}")
            # Update event status to failed
            await self.event_repository.update_status(
                event_id, 
                EventStatus.FAILED,
                validation_errors=[f"Validation error: {str(e)}"]
            )
            raise
    
    async def trigger_workflow(self, event_id: str) -> Dict[str, Any]:
        """
        Trigger workflow execution for a validated event.
        
        Args:
            event_id: Event ID
            
        Returns:
            Dict[str, Any]: Updated event with workflow information
            
        Raises:
            NotFoundError: If event or event type not found
            ValidationError: If event is not in valid state for workflow execution
            DatabaseError: If database operation fails
        """
        logger.info(f"Triggering workflow for event: {event_id}")
        
        # Get the event
        event = await self.event_repository.get_by_id(event_id)
        if not event:
            raise NotFoundError(f"Event with ID {event_id} not found")
        
        # Check if event is in valid state for workflow execution
        if event["status"] == EventStatus.VALIDATION_FAILED:
            raise ValidationError("Cannot trigger workflow for event with validation failures")
        
        # Get the event type to find associated workflows
        event_type = await self.event_type_repository.get_by_name(event["event_type"])
        if not event_type:
            raise NotFoundError(f"Event type '{event['event_type']}' not found")
        
        # Check if event type has associated workflows
        workflows = event_type.get("workflows", [])
        if not workflows:
            logger.warning(f"No workflows associated with event type: {event['event_type']}")
            # Update status to completed since no workflow to execute
            updated_event = await self.event_repository.update_status(
                event_id, 
                EventStatus.COMPLETED
            )
            return updated_event or event
        
        # For now, use the first workflow ID
        # In a more complex system, you might have logic to select the appropriate workflow
        workflow_id = workflows[0]

        try:
            # For now, we'll create a simple workflow execution without the full workflow service
            # In production, you would initialize the workflow service properly
            if self.workflow_service:
                workflow = await self.workflow_service.get_workflow(workflow_id)
                if not workflow:
                    raise NotFoundError(f"Workflow with ID {workflow_id} not found")

                # Convert workflow to WorkflowModel for execution
                workflow_model = await self.workflow_service.convert_to_workflow_model_from_workflow(workflow)
            else:
                # Create a minimal workflow model for demonstration
                from app.node.node_base.node_models import WorkflowModel
                workflow_model = WorkflowModel(
                    id=workflow_id,
                    name=f"Event Workflow for {event['event_type']}",
                    display_name=f"Event Workflow for {event['event_type']}",
                    description=f"Workflow triggered by event type: {event['event_type']}",
                    is_active=True,
                    status="active",
                    version="1.0.0",
                    initial_data=event["data"],
                    start_node="start",
                    nodes={},
                    connections={}
                )
                
                # Start workflow execution using Temporal if available
                if self.temporal_service:
                    if not self.temporal_service.is_connected():
                        await self.temporal_service.connect()
                    
                    # Generate unique workflow execution ID
                    execution_id = f"event-{event_id}-{uuid4()}"
                    
                    # Start the workflow
                    workflow_handle = await self.temporal_service.start_workflow(
                        "event_workflow",
                        workflow_model,
                        id=execution_id,
                        task_queue=self.temporal_service.task_queue
                    )
                    
                    logger.info(f"Started workflow {workflow_id} for event {event_id}")
                    
                    # Update event status
                    updated_event = await self.event_repository.update_status(
                        event_id, 
                        EventStatus.WORKFLOW_STARTED,
                        workflow_id=workflow_id
                    )
                    
                    return updated_event or event
                else:
                    logger.warning("Temporal service not available, cannot execute workflow")
                    # Update status to indicate workflow should be started but temporal is unavailable
                    updated_event = await self.event_repository.update_status(
                        event_id, 
                        EventStatus.FAILED,
                        workflow_id=workflow_id,
                        validation_errors=["Temporal service not available for workflow execution"]
                    )
                    return updated_event or event
                
        except Exception as e:
            logger.error(f"Error during workflow execution: {e}")
            # Update event status to failed
            await self.event_repository.update_status(
                event_id, 
                EventStatus.FAILED,
                validation_errors=[f"Workflow execution error: {str(e)}"]
            )
            raise
    
    async def get_event(self, event_id: str) -> Optional[Dict[str, Any]]:
        """
        Get event by ID.
        
        Args:
            event_id: Event ID
            
        Returns:
            Optional[Dict[str, Any]]: Event data or None
        """
        return await self.event_repository.get_by_id(event_id)
    
    async def get_events_by_status(
        self, 
        status: str, 
        limit: int = 100, 
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get events by status.
        
        Args:
            status: Event status
            limit: Maximum number of events to return
            skip: Number of events to skip
            
        Returns:
            List[Dict[str, Any]]: List of events
        """
        return await self.event_repository.get_by_status(status, limit, skip)
    
    async def get_events_by_correlation_id(self, correlation_id: str) -> List[Dict[str, Any]]:
        """
        Get events by correlation ID.
        
        Args:
            correlation_id: Correlation ID
            
        Returns:
            List[Dict[str, Any]]: List of events
        """
        return await self.event_repository.get_by_correlation_id(correlation_id)
