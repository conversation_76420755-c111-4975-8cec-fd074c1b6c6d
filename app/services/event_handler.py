"""
Event handler for processing events from AWS Lambda via RabbitMQ.
"""
import json
from typing import Dict, Any, Optional
from uuid import uuid4

from app.core.database import get_mongodb
from app.services.event_service import EventService

from app.services.temporal_service import TemporalService
from app.schemas.event_type import Event<PERSON><PERSON>, EventStatus
from app.utils.exceptions import NotFoundError, ValidationError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger(__name__)

async def process_event(event: Dict[Any, Any]) -> None:
    """
    Process events received from AWS Lambda via RabbitMQ.

    This function implements a complete event processing pipeline:
    1. Extract event type and data from incoming event
    2. Store event in MongoDB with "in_progress" status
    3. Validate event data against event type schema
    4. If validation succeeds, trigger workflow execution
    5. Update event status at each stage

    Args:
        event: The event payload from Lambda
    """
    logger.info(f"Processing event: {event}")

    try:
        # Extract event data from the payload
        event_data = await _extract_event_data(event)
        if not event_data:
            logger.warning("No valid event data found in payload")
            return

        # Initialize services
        mongodb = await get_mongodb()
        temporal_service = TemporalService()
        event_service = EventService(
            mongodb=mongodb,
            workflow_service=None,  # Will be initialized when needed
            temporal_service=temporal_service
        )

        # Step 1: Store event in MongoDB with initial status
        logger.info(f"Step 1: Storing event of type '{event_data.event_type}' in database")
        created_event = await event_service.create_event(event_data)
        event_id = created_event["id"]
        logger.info(f"Event stored with ID: {event_id}")

        # Step 2: Validate event data against schema
        logger.info(f"Step 2: Validating event data for event {event_id}")
        validated_event = await event_service.validate_event_data(event_id)

        # Check validation result
        if validated_event["status"] == EventStatus.VALIDATION_FAILED:
            logger.error(f"Event {event_id} validation failed: {validated_event.get('validation_errors', [])}")
            return

        logger.info(f"Event {event_id} validation successful")

        # Step 3: Trigger workflow execution
        logger.info(f"Step 3: Triggering workflow execution for event {event_id}")
        final_event = await event_service.trigger_workflow(event_id)

        if final_event["status"] == EventStatus.WORKFLOW_STARTED:
            logger.info(f"Workflow successfully started for event {event_id}, workflow_id: {final_event.get('workflow_id')}")
        elif final_event["status"] == EventStatus.COMPLETED:
            logger.info(f"Event {event_id} completed (no workflow to execute)")
        else:
            logger.warning(f"Event {event_id} ended with status: {final_event['status']}")

        logger.info(f"Event processing completed for event {event_id}")

    except NotFoundError as e:
        logger.error(f"Resource not found during event processing: {str(e)}")
    except ValidationError as e:
        logger.error(f"Validation error during event processing: {str(e)}")
    except DatabaseError as e:
        logger.error(f"Database error during event processing: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error processing event: {str(e)}", exc_info=True)


async def _extract_event_data(event: Dict[Any, Any]) -> Optional[EventCreate]:
    """
    Extract event data from the incoming event payload.

    This function handles different event payload formats and extracts
    the event type and data needed for processing.

    Args:
        event: The event payload from Lambda/RabbitMQ

    Returns:
        Optional[EventCreate]: Extracted event data or None if invalid
    """
    try:
        # Handle different payload formats
        body = event.get('body', '')

        # Case 1: Body is a JSON string
        if isinstance(body, str):
            try:
                body_data = json.loads(body)
            except json.JSONDecodeError:
                # Body is not JSON, treat as plain text
                body_data = {"message": body}
        else:
            # Body is already a dict/object
            body_data = body

        # Extract event type and data
        event_type = None
        event_data: Dict[str, Any] = {}

        # Try to extract from different possible structures
        if isinstance(body_data, dict):
            # Direct event structure
            if "event_type" in body_data:
                event_type = body_data["event_type"]
                data_field = body_data.get("data", body_data)
                event_data = data_field if isinstance(data_field, dict) else {"value": data_field}
            # Lambda event structure
            elif "eventType" in body_data:
                event_type = body_data["eventType"]
                data_field = body_data.get("data", body_data)
                event_data = data_field if isinstance(data_field, dict) else {"value": data_field}
            # Generic message structure
            elif "message" in body_data:
                event_type = "generic_message"
                event_data = body_data
            # Use the entire body as data with a default event type
            else:
                event_type = "generic_event"
                event_data = body_data
        else:
            # Non-dict body, create generic event
            event_type = "generic_event"
            event_data = {"payload": body_data}

        # Generate correlation ID from event metadata
        correlation_id = event.get('correlation_id') or event.get('requestId') or str(uuid4())

        # Extract source information
        source = event.get('source', 'lambda')

        # Create EventCreate object
        return EventCreate(
            event_type=event_type,
            data=event_data,
            source=source,
            correlation_id=correlation_id
        )

    except Exception as e:
        logger.error(f"Error extracting event data: {str(e)}")
        return None
