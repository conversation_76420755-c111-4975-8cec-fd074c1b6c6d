"""
Service for WorkFlow business logic operations.
"""

import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.workflow import WorkFlow, WorkFlowVersion
from app.repositories.workflow_repository import WorkFlowRepository, WorkFlowVersionRepository
from app.repositories.tag_repository import TagRepository
from app.services.base_service import BaseService
from app.schemas.workflow import (
    WorkFlowCreate,
    WorkFlowUpdate,
    WorkFlowVersionCreate,
    WorkFlowList,
    WorkFlowFlattenedResponse,
    SimplifiedTag
)
from app.node.node_base.node_models import WorkflowModel, NodeData, NodeConnection
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger
from app.node.node_utils.registry import node_registry

logger = get_logger("services.workflow")


class WorkFlowService(BaseService[WorkFlow, WorkFlowCreate, WorkFlowUpdate, WorkFlowRepository]):
    """Service for WorkFlow business logic operations."""
    
    def __init__(self, db: AsyncSession):
        """
        Initialize service with database session.
        
        Args:
            db: Database session
        """
        self.db = db
        self.workflow_repository = WorkFlowRepository(db)
        self.version_repository = WorkFlowVersionRepository(db)
        self.tag_repository = TagRepository(db)
        super().__init__(self.workflow_repository)
    
    async def create_workflow(
    self,
        workflow_data: WorkFlowCreate
    ) -> WorkFlow:
        """
        Create a new workflow with its initial version in a single transaction.

        Args:
            workflow_data: Workflow creation data (may include work_flow definition)
            initial_version_data: Legacy parameter for initial version workflow definition (deprecated)

        Returns:
            WorkFlow: Created workflow with initial version

        Raises:
            ConflictError: If workflow name already exists
            ValidationError: If tag IDs are invalid
            ValidationError: If work_flow_id is provided but doesn't exist or doesn't belong to this workflow
        """
        try:
            # Check if workflow name already exists
            existing_workflow = await self.workflow_repository.get_by_name(workflow_data.name)
            if existing_workflow:
                raise ConflictError(f"Workflow with name '{workflow_data.name}' already exists")
            
            # Validate tag IDs if provided
            if workflow_data.tag_ids:
                await self._validate_tag_ids(workflow_data.tag_ids)

            # Validate node types if workflow definition is provided
            if workflow_data.work_flow and workflow_data.work_flow.nodes:
                self._validate_node_types(workflow_data.work_flow.nodes)

            # Create workflow
            workflow_dict = workflow_data.model_dump(exclude={"tag_ids", "work_flow"})
            workflow = WorkFlow(**workflow_dict)

            self.db.add(workflow)
            await self.db.flush()  # Get the UID
            await self.db.refresh(workflow)  # Ensure the UID is properly loaded

            # Handle workflow definition
            if workflow_data.work_flow:
                if not workflow.uid:
                    raise ValidationError("Failed to generate workflow UID")
    

                initial_version = WorkFlowVersion(
                    version_no=1,
                    work_flow=workflow_data.work_flow.model_dump(exclude={"version_name"}),
                    workflow_id=workflow.uid,
                    version_name=workflow_data.work_flow.version_name,
                    # version_tag_id=workflow_data.work_flow.version_tag_id,
                    created_by=workflow_data.created_by,
                    edited_by=workflow_data.created_by
                )
                self.db.add(initial_version)
                await self.db.flush()
            else:
                raise ValidationError("Workflow definition (work_flow) is required for initial version creation")

            # Set active version
            workflow.active_version_id = initial_version.id
            # Add tags if provided
            if workflow_data.tag_ids:
                await self.workflow_repository.add_tags(workflow, workflow_data.tag_ids)
            
            await self.db.commit()
            await self.db.refresh(workflow)
            
            # Load relationships
            workflow_with_relations = await self.workflow_repository.get_with_versions(workflow.uid)
            if workflow_with_relations is None:
                raise ValueError(f"Failed to retrieve created workflow with ID {workflow.uid}")
            
            logger.info(
                "Workflow created with initial version",
                workflow_id=workflow.uid,
                workflow_name=workflow.name,
                created_by=workflow.created_by
            )
            
            return workflow_with_relations
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create workflow: {str(e)}")
            raise
    
    async def get_workflow(self, workflow_id: uuid.UUID) -> WorkFlow:
        """
        Get workflow with all its versions and relationships.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            WorkFlow: Workflow with versions and tags
            
        Raises:
            NotFoundError: If workflow not found
        """
        workflow = await self.workflow_repository.get_with_versions(workflow_id)
        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")
        
        return workflow
    
    async def update_workflow(
        self,
        workflow_id: uuid.UUID,
        workflow_update: WorkFlowUpdate,
        new_version_data: Optional[Dict[str, Any]] = None,
        version_tag_id: Optional[int] = None
    ) -> WorkFlow:
        """
        Update workflow and create a new version if workflow logic changes.
        
        Args:
            workflow_id: Workflow UID
            workflow_update: Workflow update data
            new_version_data: New version workflow definition
            version_tag_id: Optional tag ID for the new version
            
        Returns:
            WorkFlow: Updated workflow
            
        Raises:
            NotFoundError: If workflow not found
            ValidationError: If update data is invalid
        """
        try:
            # Get existing workflow
            workflow = await self.workflow_repository.get_with_versions(workflow_id)
            if not workflow:
                raise NotFoundError(f"Workflow with id {workflow_id} not found")
            
            # Update workflow metadata
            update_dict = workflow_update.model_dump(exclude_unset=True, exclude={"tag_ids", "work_flow"})
            for field, value in update_dict.items():
                setattr(workflow, field, value)

            # Create new version if workflow definition provided (either through work_flow field or legacy new_version_data)
            if workflow_update.work_flow or new_version_data:
                # Validate node types if workflow definition is provided
                if workflow_update.work_flow and workflow_update.work_flow.nodes:
                    self._validate_node_types(workflow_update.work_flow.nodes)
                elif new_version_data and isinstance(new_version_data, dict) and new_version_data.get('nodes'):
                    self._validate_node_types(new_version_data['nodes'])

                latest_version_no = await self.version_repository.get_latest_version_number(workflow_id)
                new_version_no = latest_version_no + 1

                # Use work_flow field if provided, otherwise fall back to new_version_data
                if workflow_update.work_flow:
                    version_data = WorkFlowVersionCreate(
                        version_no=new_version_no,
                        work_flow=workflow_update.work_flow.model_dump(exclude={"version_name"}),
                        version_name=workflow_update.work_flow.version_name,
                        workflow_id=workflow_id,
                        created_by=workflow_update.edited_by
                    )
                else:
                    version_data = WorkFlowVersionCreate(
                        version_no=new_version_no,
                        work_flow=new_version_data,
                        workflow_id=workflow_id,
                        version_tag_id=version_tag_id,
                        created_by=workflow_update.edited_by
                    )

                version_dict = version_data.model_dump()
                new_version = WorkFlowVersion(**version_dict)

                self.db.add(new_version)
                await self.db.flush()

                # Update active version
                workflow.active_version_id = new_version.id
            
            # Handle tag updates
            if hasattr(workflow_update, 'tag_ids') and workflow_update.tag_ids is not None:
                await self._update_workflow_tags(workflow, workflow_update.tag_ids)
            
            await self.db.commit()
            await self.db.refresh(workflow)
            
            # Reload with relationships
            updated_workflow = await self.workflow_repository.get_with_versions(workflow_id)
            
            logger.info(
                "Workflow updated",
                workflow_id=workflow_id,
                new_version_created=new_version_data is not None,
                updated_by=workflow_update.edited_by if hasattr(workflow_update, 'edited_by') else None
            )
            
            return updated_workflow

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update workflow {workflow_id}: {str(e)}")
            raise

    async def update_workflow_unified(
        self,
        workflow_id: uuid.UUID,
        workflow_data: WorkFlowCreate
    ) -> WorkFlow:
        """
        Update workflow using the unified schema structure (same as creation).

        This method handles updating both workflow metadata and version data
        using the same request structure as the POST endpoint.

        Args:
            workflow_id: Workflow UID to update
            workflow_data: Complete workflow data including metadata and work_flow definition

        Returns:
            WorkFlow: Updated workflow with relationships loaded

        Raises:
            NotFoundError: If workflow not found
            ValidationError: If update data is invalid
            ConflictError: If workflow name conflicts with another workflow
        """
        try:
            # Get existing workflow
            workflow = await self.workflow_repository.get_with_versions(workflow_id)
            if not workflow:
                raise NotFoundError(f"Workflow with id {workflow_id} not found")

            # Check for name conflicts (only if name is being changed)
            if workflow_data.name and workflow_data.name != workflow.name:
                existing_workflow = await self.workflow_repository.get_by_name(workflow_data.name)
                if existing_workflow and existing_workflow.uid != workflow_id:
                    raise ConflictError(f"Workflow with name '{workflow_data.name}' already exists")

            # Update workflow metadata fields
            if workflow_data.name:
                workflow.name = workflow_data.name
            if workflow_data.description is not None:
                workflow.description = workflow_data.description
            if workflow_data.is_active is not None:
                workflow.is_active = workflow_data.is_active
            if workflow_data.status is not None:
                workflow.status = workflow_data.status
            if workflow_data.edited_by is not None:
                workflow.edited_by = workflow_data.edited_by

            # Handle version update/creation if work_flow is provided
            if workflow_data.work_flow:
                # Check if we're updating an existing version or creating a new one
                if workflow_id:
                    # Update existing version
                    existing_version = await self.version_repository.get(workflow_data.work_flow.work_flow_version_id)
                    if not existing_version or existing_version.workflow_id != workflow_id:
                        raise ValidationError(f"Version with id {workflow_id} does not exist or does not belong to this workflow")

                    # Update version fields
                    if workflow_data.work_flow.version_name is not None:
                        existing_version.version_name = workflow_data.work_flow.version_name

                    # Update work_flow definition
                    work_flow_dict = {
                        "start_node": workflow_data.work_flow.start_node,
                        "nodes": workflow_data.work_flow.nodes,
                        "connections": workflow_data.work_flow.connections
                    }
                    existing_version.work_flow = work_flow_dict
                    existing_version.edited_by = workflow_data.edited_by

                    # Set as active version if not already
                    if workflow.active_version_id != existing_version.id:
                        workflow.active_version_id = existing_version.id

                else:
                    # Create new version
                    latest_version_no = await self.version_repository.get_latest_version_number(workflow_id)
                    new_version_no = latest_version_no + 1

                    work_flow_dict = {
                        "start_node": workflow_data.work_flow.start_node,
                        "nodes": workflow_data.work_flow.nodes,
                        "connections": workflow_data.work_flow.connections
                    }

                    version_data = WorkFlowVersionCreate(
                        version_no=new_version_no,
                        work_flow=work_flow_dict,
                        version_name=workflow_data.work_flow.version_name,
                        workflow_id=workflow_id,
                        created_by=workflow_data.edited_by
                    )

                    version_dict = version_data.model_dump()
                    new_version = WorkFlowVersion(**version_dict)

                    self.db.add(new_version)
                    await self.db.flush()

                    # Set as active version
                    workflow.active_version_id = new_version.id
            # Handle tag updates
            if workflow_data.tag_ids is not None:
                await self._update_workflow_tags(workflow, workflow_data.tag_ids)

            await self.db.commit()
            await self.db.refresh(workflow)

            # Reload with relationships
            updated_workflow = await self.workflow_repository.get_with_versions(workflow_id)

            logger.info(
                "Workflow updated via unified method",
                workflow_id=workflow_id,
                workflow_name=updated_workflow.name,
                version_updated=workflow_data.work_flow is not None,
                updated_by=workflow_data.edited_by
            )
            return updated_workflow

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update workflow {workflow_id} via unified method: {str(e)}")
            raise
    
    async def get_workflows_paginated(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        active_only: bool = True
    ) -> WorkFlowList:
        """
        Get paginated list of workflows.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Additional filters
            active_only: Whether to return only active workflows
            
        Returns:
            WorkFlowList: Paginated workflow list
        """
        if active_only:
            workflows = await self.workflow_repository.get_active_workflows(skip, limit, filters)
            total = await self.workflow_repository.count_active_workflows(filters)
        else:
            workflows = await self.workflow_repository.get_multi(skip, limit, filters)
            total = await self.workflow_repository.count(filters)
        
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return WorkFlowList(
            workflows=workflows,
            total=total,
            page=page,
            size=limit
        )
    
    async def deactivate_workflow(self, workflow_id: uuid.UUID) -> WorkFlow:
        """
        Deactivate a workflow.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            WorkFlow: Deactivated workflow
            
        Raises:
            NotFoundError: If workflow not found
        """
        workflow = await self.workflow_repository.get_by_uid(workflow_id)
        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")
        
        workflow.is_active = False
        await self.db.commit()
        await self.db.refresh(workflow)
        
        logger.info("Workflow deactivated", workflow_id=workflow_id)
        return workflow
    
    async def _validate_tag_ids(self, tag_ids: List[int]) -> None:
        """
        Validate that all provided tag IDs exist.

        Args:
            tag_ids: List of tag IDs to validate

        Raises:
            ValidationError: If any tag ID is invalid
        """
        for tag_id in tag_ids:
            tag_exists = await self.tag_repository.exists(tag_id)
            if not tag_exists:
                raise ValidationError(f"Tag with id {tag_id} does not exist")

    def _validate_node_types(self, nodes: Dict[str, Dict[str, Any]]) -> None:
        """
        Validate that all node types in the workflow are registered and valid.

        Args:
            nodes: Dictionary of nodes with node IDs as keys and node data as values

        Raises:
            ValidationError: If any node type is invalid or not registered
        """
        if not nodes:
            return

        for node_id, node_data in nodes.items():
            if not isinstance(node_data, dict):
                raise ValidationError(f"Node '{node_id}' data must be a dictionary")

            node_type = node_data.get('type')
            if not node_type:
                raise ValidationError(f"Node '{node_id}' is missing required 'type' field")

            if not isinstance(node_type, str):
                raise ValidationError(f"Node '{node_id}' type must be a string, got {type(node_type).__name__}")

            # Get node class from registry
            node_class = node_registry.get_node_class(node_type)
            if not node_class:
                raise ValidationError(f"Node type '{node_type}' is not registered in the system")

            # Validate the node type by calling the validate method
            try:
                # Create a minimal NodeRequest for validation
                from app.node.node_base.node_models import NodeRequest
                node_request = NodeRequest(
                    node_type=node_type,
                    parameters=node_data.get('parameters', {}),
                    position=node_data.get('position', [0, 0]),
                    id=node_id
                )

                # Create node instance and validate
                node_instance = node_class()
                validation_result = node_instance.validate(node_request)

                if not validation_result.valid:
                    error_messages = []
                    if validation_result.errors:
                        error_messages = [f"{error.parameter}: {error.message}" for error in validation_result.errors]
                    raise ValidationError(f"Node '{node_id}' of type '{node_type}' validation failed: {'; '.join(error_messages)}")

            except Exception as e:
                if isinstance(e, ValidationError):
                    raise
                raise ValidationError(f"Node type '{node_type}' validation failed for node '{node_id}': {str(e)}")
    
    async def _update_workflow_tags(self, workflow: WorkFlow, new_tag_ids: List[int]) -> None:
        """
        Update workflow tags by replacing current tags with new ones.
        
        Args:
            workflow: Workflow instance
            new_tag_ids: New list of tag IDs
        """
        # Validate new tag IDs
        if new_tag_ids:
            await self._validate_tag_ids(new_tag_ids)
        
        # Get current tag IDs
        current_tag_ids = [tag.id for tag in workflow.tags]
        
        # Remove tags that are no longer needed
        tags_to_remove = [tag_id for tag_id in current_tag_ids if tag_id not in new_tag_ids]
        if tags_to_remove:
            await self.workflow_repository.remove_tags(workflow, tags_to_remove)
        
        # Add new tags
        tags_to_add = [tag_id for tag_id in new_tag_ids if tag_id not in current_tag_ids]
        if tags_to_add:
            await self.workflow_repository.add_tags(workflow, tags_to_add)

    def to_flattened_response(self, workflow: WorkFlow) -> WorkFlowFlattenedResponse:
        """
        Convert a WorkFlow object to a flattened response format.

        This method combines workflow metadata with active version data into a single
        flattened structure, making it easier for frontend consumption.

        Args:
            workflow: WorkFlow object with loaded active_version and tags relationships

        Returns:
            WorkFlowFlattenedResponse: Flattened response with combined data

        Raises:
            ValidationError: If workflow doesn't have an active version
        """
        if not workflow.active_version:
            raise ValidationError("Workflow must have an active version for flattened response")

        # Convert tags to simplified format
        simplified_tags = [
            SimplifiedTag(id=tag.id, name=tag.name)
            for tag in workflow.tags
        ]

        # Create flattened response
        return WorkFlowFlattenedResponse(
            # Workflow metadata fields
            created_at=workflow.created_at.isoformat() + "Z",
            updated_at=workflow.updated_at.isoformat() + "Z",
            name=workflow.name,
            is_active=workflow.is_active,
            description=workflow.description,
            status=workflow.status,
            created_by=workflow.created_by,
            edited_by=workflow.edited_by,
            uid=str(workflow.uid),

            # Simplified tags
            tags=simplified_tags,

            # Active version fields (flattened)
            active_version_id=workflow.active_version.id,
            version_no=workflow.active_version.version_no,
            version_name=workflow.active_version.version_name,
            work_flow=workflow.active_version.work_flow
        )

    async def convert_to_workflow_model(self, workflow_data: WorkFlowCreate) -> WorkflowModel:
        """
        Convert a WorkFlowCreate schema to a WorkflowModel for execution.

        This method transforms the API schema format to the execution model format,
        converting nodes and connections to the structure expected by the workflow executor.

        Args:
            workflow_data: WorkFlowCreate schema with workflow definition

        Returns:
            WorkflowModel: Converted workflow model ready for execution

        Raises:
            ValidationError: If workflow definition is missing or invalid
        """
        from app.node.node_base.node_models import NodeData, NodeConnection

        if not workflow_data.work_flow:
            raise ValidationError("Workflow definition (work_flow) is required for test execution")

        work_flow_def = workflow_data.work_flow

        if not work_flow_def.start_node:
            raise ValidationError("Start node is required for workflow execution")

        if not work_flow_def.nodes:
            raise ValidationError("At least one node is required for workflow execution")

        # Convert nodes from Dict[str, Dict[str, Any]] to Dict[str, NodeData]
        converted_nodes = {}
        for node_id, node_data in work_flow_def.nodes.items():
            if not isinstance(node_data, dict):
                raise ValidationError(f"Node '{node_id}' data must be a dictionary")

            # Extract required fields with defaults
            node_type = node_data.get('type')
            if not node_type:
                raise ValidationError(f"Node '{node_id}' is missing required 'type' field")

            position = node_data.get('position', [0, 0])
            parameters = node_data.get('parameters', {})

            # Create NodeData instance
            converted_nodes[node_id] = NodeData(
                name=node_id,
                type=node_type,
                position=position,
                parameters=parameters,
                disabled=node_data.get('disabled', False)
            )

        # Convert connections from Dict[str, Dict[str, List[List[str]]]] to Dict[str, NodeConnection]
        converted_connections = {}
        if work_flow_def.connections:
            for node_id, connection_data in work_flow_def.connections.items():
                if isinstance(connection_data, dict) and 'main' in connection_data:
                    converted_connections[node_id] = NodeConnection(main=connection_data['main'])
                else:
                    # Handle legacy format or create empty connection
                    converted_connections[node_id] = NodeConnection(main=[])

        # Create WorkflowModel
        workflow_model = WorkflowModel(
            id=str(uuid.uuid4()),  # Generate unique ID for test execution
            name=workflow_data.name,
            display_name=workflow_data.name,
            description=workflow_data.description or "",
            is_active=workflow_data.is_active,
            status=workflow_data.status,
            version="test-1.0.0",
            initial_data={},  # initial_data is not available in WorkFlowCreate schema
            start_node=work_flow_def.start_node,
            nodes=converted_nodes,
            connections=converted_connections
        )

        return workflow_model

    async def convert_to_workflow_model_from_workflow(self, workflow: WorkFlow) -> WorkflowModel:
        """
        Convert a WorkFlow model to a WorkflowModel for execution.

        This method transforms the database model to the execution model format,
        using the active version of the workflow.

        Args:
            workflow: WorkFlow database model with active version

        Returns:
            WorkflowModel: Workflow model ready for execution

        Raises:
            ValueError: If workflow has no active version or invalid structure
        """
        if not workflow.active_version:
            raise ValueError(f"Workflow {workflow.uid} has no active version")

        active_version = workflow.active_version
        work_flow_def = active_version.work_flow

        if not work_flow_def:
            raise ValueError(f"Workflow version {active_version.id} has no workflow definition")

        # Convert nodes
        converted_nodes = {}
        if work_flow_def.get("nodes"):
            for node_id, node_data in work_flow_def["nodes"].items():
                converted_nodes[node_id] = NodeData(**node_data)

        # Convert connections
        converted_connections = {}
        if work_flow_def.get("connections"):
            for conn_id, conn_data in work_flow_def["connections"].items():
                converted_connections[conn_id] = NodeConnection(**conn_data)

        # Create WorkflowModel
        workflow_model = WorkflowModel(
            id=str(workflow.uid),
            name=workflow.name,
            display_name=workflow.name,
            description=workflow.description or "",
            is_active=workflow.is_active,
            status=workflow.status,
            version=f"{active_version.version_no}.0.0",
            initial_data={},
            start_node=work_flow_def.get("start_node", ""),
            nodes=converted_nodes,
            connections=converted_connections
        )

        return workflow_model
