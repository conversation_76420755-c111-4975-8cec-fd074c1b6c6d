"""
Event Type Pydantic schemas for request/response validation.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import Field, field_validator

from app.schemas.common import BaseSchema, PaginatedResponse


class JSONSchema(BaseSchema):
    """Comprehensive JSON Schema model with validation."""

    # Core schema fields
    schema_: Optional[str] = Field(None, alias="$schema", description="JSON Schema version")
    id: Optional[str] = Field(None, alias="$id", description="Schema identifier")
    ref: Optional[str] = Field(None, alias="$ref", description="Schema reference")

    # Type is required for root schemas
    type: str = Field(..., description="Schema type")

    # Common validation fields
    format: Optional[str] = Field(None, description="String format validation")
    enum: Optional[List[Union[str, int, float, bool]]] = Field(None, description="Allowed values")
    const: Optional[Union[str, int, float, bool]] = Field(None, description="Constant value")

    # String validation
    minLength: Optional[int] = Field(None, ge=0, description="Minimum string length")
    maxLength: Optional[int] = Field(None, ge=0, description="Maximum string length")
    pattern: Optional[str] = Field(None, description="Regular expression pattern")

    # Numeric validation
    minimum: Optional[Union[int, float]] = Field(None, description="Minimum numeric value")
    maximum: Optional[Union[int, float]] = Field(None, description="Maximum numeric value")
    exclusiveMinimum: Optional[Union[int, float]] = Field(None, description="Exclusive minimum value")
    exclusiveMaximum: Optional[Union[int, float]] = Field(None, description="Exclusive maximum value")
    multipleOf: Optional[Union[int, float]] = Field(None, gt=0, description="Multiple of value")

    # Array validation
    minItems: Optional[int] = Field(None, ge=0, description="Minimum array items")
    maxItems: Optional[int] = Field(None, ge=0, description="Maximum array items")
    uniqueItems: Optional[bool] = Field(None, description="Array items must be unique")
    items: Optional[Dict[str, Any]] = Field(None, description="Array item schema")

    # Object validation
    properties: Optional[Dict[str, Dict[str, Any]]] = Field(None, description="Object properties")
    required: Optional[List[str]] = Field(None, description="Required properties")
    additionalProperties: Optional[Union[bool, Dict[str, Any]]] = Field(None, description="Additional properties")
    minProperties: Optional[int] = Field(None, ge=0, description="Minimum object properties")
    maxProperties: Optional[int] = Field(None, ge=0, description="Maximum object properties")

    # Metadata
    title: Optional[str] = Field(None, description="Schema title")
    description: Optional[str] = Field(None, description="Schema description")
    default: Optional[Any] = Field(None, description="Default value")
    examples: Optional[List[Any]] = Field(None, description="Example values")

    @field_validator('type')
    @classmethod
    def validate_type(cls, v):
        """Validate schema type."""
        valid_types = ['null', 'boolean', 'object', 'array', 'number', 'integer', 'string']
        if v not in valid_types:
            raise ValueError(f'Type must be one of: {", ".join(valid_types)}')
        return v

    @field_validator('format')
    @classmethod
    def validate_format(cls, v):
        """Validate format for string types."""
        if v is not None:
            valid_formats = [
                'date-time', 'date', 'time', 'email', 'hostname', 'ipv4', 'ipv6',
                'uri', 'uri-reference', 'uuid', 'regex', 'json-pointer'
            ]
            if v not in valid_formats:
                raise ValueError(f'Format must be one of: {", ".join(valid_formats)}')
        return v

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for jsonschema library."""
        return self.model_dump(by_alias=True, exclude_none=True)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'JSONSchema':
        """Create JSONSchema from dictionary."""
        return cls(**data)


class EventTypeBase(BaseSchema):
    """Base event type schema with common fields."""

    name: str = Field(..., min_length=1, max_length=100, description="Unique identifier for the event type")
    description: str = Field(..., min_length=1, max_length=500, description="Human-readable description")
    schema: JSONSchema = Field(..., description="JSON schema definition")
    version: int = Field(default=1, ge=1, description="Schema version number")
    workflows: Optional[List[str]] = Field(default_factory=list, description="List of workflow IDs using this event type")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate event type name format."""
        if v is not None:
            print(f"Validating name: {v}")
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Name must contain only alphanumeric characters, hyphens, and underscores')
            return v.lower()
        return v


class EventTypeCreate(EventTypeBase):
    """Schema for creating a new event type."""
    
    created_by: int = Field(..., description="ID of the user creating the event type")


class EventTypeUpdate(BaseSchema):
    """Schema for updating an existing event type."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1, max_length=500)
    schema: Optional[JSONSchema] = Field(None, description="Updated JSON schema definition")
    # edited_by: int = Field(..., description="ID of the user editing the event type")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate event type name format."""
        if v is not None:
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Name must contain only alphanumeric characters, hyphens, and underscores')
            return v.lower()
        return v

class EventTypeDBUpdate(EventTypeUpdate):
    """Schema for updating an existing event type in the database."""
    
    edited_by: int = Field(..., description="ID of the user editing the event type")


class EventType(EventTypeBase):
    """Schema for event type response."""
    
    id: str = Field(..., description="MongoDB ObjectId as string")
    created_by: int
    edited_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for jsonschema library."""
        return self.model_dump(by_alias=True, exclude_none=True)


class EventTypeDetail(EventType):
    """Detailed event type schema with additional metadata."""
    
    # Additional fields can be added here for detailed view
    pass


class EventTypeList(BaseSchema):
    """Schema for event type list item."""
    
    id: str
    name: str
    description: str
    version: int
    created_by: int
    created_at: datetime
    updated_at: datetime
    workflows: Optional[list[str]] = None


class ValidationRequest(BaseSchema):
    """Schema for JSON validation request."""
    
    data: Union[Dict[str, Any], List[Any], str, int, float, bool, None] = Field(
        ..., 
        description="JSON data to validate against the event type schema"
    )


class ValidationError(BaseSchema):
    """Schema for validation error details."""
    
    field: str = Field(..., description="Field path where validation failed")
    message: str = Field(..., description="Error message")
    invalid_value: Optional[Any] = Field(None, description="The invalid value that caused the error")


class ValidationResponse(BaseSchema):
    """Schema for validation response."""
    
    valid: bool = Field(..., description="Whether the data is valid")
    errors: List[ValidationError] = Field(default_factory=list, description="List of validation errors")
    
    
# Type aliases for paginated responses
EventTypeListResponse = PaginatedResponse[EventTypeList]


# Event processing schemas
class EventStatus:
    """Event processing status constants."""
    IN_PROGRESS = "in_progress"
    VALIDATION_FAILED = "validation_failed"
    WORKFLOW_STARTED = "workflow_started"
    COMPLETED = "completed"
    FAILED = "failed"


class EventBase(BaseSchema):
    """Base event schema."""

    event_type: str = Field(..., description="Event type identifier")
    data: Dict[str, Any] = Field(..., description="Event data payload")
    source: Optional[str] = Field(default=None, description="Event source")
    correlation_id: Optional[str] = Field(default=None, description="Correlation ID for tracking")


class EventCreate(EventBase):
    """Schema for creating a new event."""
    pass


class Event(EventBase):
    """Schema for event response."""

    id: str = Field(..., description="MongoDB ObjectId as string")
    status: str = Field(..., description="Event processing status")
    workflow_id: Optional[str] = Field(default=None, description="Associated workflow ID")
    validation_errors: Optional[List[str]] = Field(default=None, description="Validation error messages")
    created_at: datetime
    updated_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
