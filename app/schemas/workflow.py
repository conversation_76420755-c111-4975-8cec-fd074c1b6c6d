"""
Workflow-related Pydantic schemas for request/response validation.
"""

import uuid
from typing import Any, Dict, List, Optional

from pydantic import Field

from app.schemas.common import BaseSchema, IDMixin, TimestampMixin
from app.schemas.tag import Tag
from app.node.node_base.node_models import WorkflowStatus


class SimplifiedTag(BaseSchema):
    """Simplified tag schema for flattened workflow responses."""

    id: int = Field(..., description="Tag ID")
    name: str = Field(..., description="Tag name")


class WorkFlowBase(BaseSchema):
    """Base workflow schema with common fields."""

    name: str = Field(..., min_length=1, max_length=255, description="Workflow name")
    is_active: bool = Field(default=True, description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    status: WorkflowStatus = Field(default=WorkflowStatus.DRAFT, description="Workflow status")
    initial_data: Optional[Dict[str, Any]] = Field(None, description="Initial workflow configuration data")
    created_by: int = Field(..., description="ID of the user who created this workflow")
    edited_by: Optional[int] = Field(None, description="ID of the user who last edited this workflow")


class WorkFlowDefinition(BaseSchema):
    """Schema for workflow definition with version management."""

    work_flow_id: Optional[int] = Field(None, description="If provided, update this existing version; if not provided, create a new version")
    work_flow_version_id: Optional[int] = Field(None, description="ID of the workflow version to update; if not provided, create a new version")
    start_node: Optional[str] = Field(None, description="ID of the starting node for workflow execution")
    nodes: Dict[str, Dict[str, Any]] = Field(description="Dictionary of nodes with node IDs as keys")
    connections: Dict[str, Dict[str, List[List[str]]]] = Field(description="Dictionary of connections with node IDs as keys")
    # nodes: List[Dict[str, Any]] = Field(..., description="The workflow node definitions")
    # connections: List[Dict[str, Any]] = Field(..., description="The workflow connections")
    version_name: Optional[str] = Field(None, description="Name for this version")

    model_config = {
        "json_schema_extra": {
            "example": {
                "work_flow_id": 123,
                "start_node": "wait_1",
                "version_name": "Updated Version",
                "nodes": [
                    {
                        "id": "start",
                        "type": "trigger",
                        "position": [100, 100],
                        "config": {
                            "webhook_url": "https://example.com/webhook"
                        }
                    },
                    {
                        "id": "process",
                        "type": "action",
                        "position": [300, 100],
                        "config": {
                            "action_type": "email",
                            "template": "welcome_email"
                        }
                    }
                ],
                "connections": [
                    {
                        "from": "start",
                        "to": "process"
                    }
                ],
                # "version_tag_id": 5
            }
        }
    }


class WorkFlowCreate(BaseSchema):
    """Schema for creating a new workflow."""

    name: str = Field(..., min_length=1, max_length=255, description="Workflow name")
    is_active: bool = Field(default=True, description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    status: WorkflowStatus = Field(default=WorkflowStatus.DRAFT, description="Workflow status")
    # initial_data: Optional[Dict[str, Any]] = Field(None, description="Initial workflow configuration data")
    created_by: Optional[int] = Field(None, description="ID of the user who created this workflow (set automatically)")
    edited_by: Optional[int] = Field(None, description="ID of the user who last edited this workflow (set automatically)")
    tag_ids: Optional[List[int]] = Field(default=[], description="List of tag IDs to associate with the workflow")
    work_flow: Optional[WorkFlowDefinition] = Field(None, description="Workflow definition for creating or updating a version")

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Customer Onboarding Workflow",
                "is_active": True,
                "description": "Automated workflow for onboarding new customers",
                "status": "draft",
                "initial_data": {
                    "trigger": "webhook",
                    "environment": "production"
                },
                "tag_ids": [1, 2, 3],
                "work_flow": {
                    "work_flow_id": None,
                    "start_node": "wait_1",
                    "version_name": "Initial Version",
                    "nodes": [
                        {
                            "id": "start",
                            "type": "trigger",
                            "position": [100, 100],
                            "config": {
                                "webhook_url": "https://example.com/webhook"
                            }
                        }
                    ],
                    "connections": [],
                    # "version_tag_id": 5
                }
            }
        }
    }


class WorkFlowUpdate(BaseSchema):
    """Schema for updating an existing workflow."""

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Workflow name")
    is_active: Optional[bool] = Field(None, description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    status: Optional[WorkflowStatus] = Field(None, description="Workflow status")
    tag_ids: Optional[List[int]] = Field(None, description="List of tag IDs to associate with the workflow")
    active_version_id: Optional[int] = Field(None, description="ID of the active version")
    edited_by: Optional[int] = Field(None, description="ID of the user who is editing this workflow (set automatically)")
    work_flow: Optional[WorkFlowDefinition] = Field(None, description="Workflow definition for creating or updating a version")


class WorkFlowVersionBase(BaseSchema):
    """Base workflow version schema with common fields."""

    version_no: int = Field(..., ge=1, description="Version number")
    work_flow: Dict[str, Any] = Field(..., description="Complete workflow definition")
    version_name: Optional[str] = Field(None, description="Name for this version")
    created_by: Optional[int] = Field(None, description="ID of the user who created this version (set automatically)")
    edited_by: Optional[int] = Field(None, description="ID of the user who last edited this version")


class WorkFlowVersionCreate(BaseSchema):
    """Schema for creating a new workflow version."""

    version_no: int = Field(..., ge=1, description="Version number")
    work_flow: Dict[str, Any] = Field(..., description="Complete workflow definition")
    version_name: Optional[str] = Field(None, description="Name for this version")
    created_by: Optional[int] = Field(None, description="ID of the user who created this version (set automatically)")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "version_no": 1,
                "work_flow": {
                    "nodes": [
                        {
                            "id": "start",
                            "type": "trigger",
                            "position": [100, 100]
                        }
                    ],
                    "connections": {}
                },
                "version_name": "Initial Version"
            }
        }
    }


class WorkFlowVersionUpdate(BaseSchema):
    """Schema for updating an existing workflow version."""

    version_no: Optional[int] = Field(None, ge=1, description="Version number")
    work_flow: Optional[Dict[str, Any]] = Field(None, description="Complete workflow definition")
    version_name: Optional[str] = Field(None, description="Name for this version")
    edited_by: int = Field(..., description="ID of the user who is editing this version")


class WorkFlowVersionInDB(WorkFlowVersionBase, IDMixin, TimestampMixin):
    """Schema for workflow version as stored in the database."""

    workflow_id: uuid.UUID = Field(..., description="ID of the parent workflow")


class WorkFlowVersion(WorkFlowVersionInDB):
    """Schema for workflow version response."""
    pass


class WorkFlowVersionDetail(WorkFlowVersionInDB):
    """Schema for detailed workflow version response with relationships."""
    pass


class WorkFlowInDB(WorkFlowBase, TimestampMixin):
    """Schema for workflow as stored in the database."""
    
    uid: uuid.UUID = Field(..., description="Unique identifier for the workflow")
    active_version_id: Optional[int] = Field(None, description="ID of the active version")


class WorkFlow(WorkFlowInDB):
    """Schema for workflow response."""
    
    tags: List[Tag] = Field(default=[], description="Associated tags")


class WorkFlowDetail(WorkFlowInDB):
    """Schema for detailed workflow response with relationships."""

    tags: List[Tag] = Field(default=[], description="Associated tags")
    versions: List[WorkFlowVersion] = Field(default=[], description="All versions of this workflow")
    active_version: Optional[WorkFlowVersion] = Field(None, description="Currently active version")


class WorkFlowFlattenedResponse(BaseSchema):
    """Schema for flattened workflow response combining workflow and active version data."""

    # Workflow fields
    created_at: str = Field(..., description="Workflow creation timestamp")
    updated_at: str = Field(..., description="Workflow last update timestamp")
    name: str = Field(..., description="Workflow name")
    is_active: bool = Field(..., description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    status: WorkflowStatus = Field(..., description="Workflow status")
    created_by: int = Field(..., description="ID of the user who created this workflow")
    edited_by: Optional[int] = Field(None, description="ID of the user who last edited this workflow")
    uid: str = Field(..., description="Unique identifier for the workflow")

    # Simplified tags (only id and name)
    tags: List[SimplifiedTag] = Field(default=[], description="Associated tags with simplified structure")

    # Active version fields (renamed id to active_version_id for clarity)
    active_version_id: int = Field(..., description="Active version ID")
    version_no: int = Field(..., description="Active version number")
    version_name: Optional[str] = Field(None, description="Active version name")
    work_flow: Dict[str, Any] = Field(..., description="Complete workflow definition with nodes and connections")

    model_config = {
        "json_schema_extra": {
            "example": {
                "created_at": "2025-07-07T10:37:32.648679Z",
                "updated_at": "2025-07-07T10:37:32.648679Z",
                "name": "inventory_workflow_171_100",
                "is_active": True,
                "created_by": 1,
                "edited_by": None,
                "uid": "7f5bcb4c-e442-47c9-82e2-1765cb5113be",
                "tags": [
                    {
                        "id": 1,
                        "name": "production"
                    }
                ],
                "active_version_id": 33,
                "version_no": 1,
                "work_flow": {
                    "start_node": "wait_1",
                    "nodes": {
                        "wait_1": {
                            "name": "wait_1",
                            "type": "wait",
                            "version": 1,
                            "position": [-543, 920],
                            "parameters": {
                                "date_time": "2025-06-20T07:44:00.000Z",
                                "wait_unit": "seconds",
                                "wait_value": 15,
                                "resume_type": "specific_time"
                            },
                            "description": "Pauses workflow execution for a specified duration or until a specific time.",
                            "display_name": "Wait"
                        }
                    },
                    "connections": {
                        "wait_1": {
                            "main": [
                                ["wait_2", "wait_3"]
                            ]
                        }
                    },
                    "work_flow_id": None,
                    "version_tag_id": None
                }
            }
        }
    }


class WorkFlowList(BaseSchema):
    """Schema for workflow list response."""

    workflows: List[WorkFlow]
    total: int
    page: int
    size: int

    model_config = {
        "json_schema_extra": {
            "example": {
                "workflows": [],
                "total": 0,
                "page": 1,
                "size": 10
            }
        }
    }



