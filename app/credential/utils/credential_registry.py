from typing import Any, Callable, Optional, Type, List

from app.credential.base.credential_model import CredentialModel


class CredentialRegistry:
    
    _credentials: dict[str, Type['CredentialModel']] = {}

    @classmethod
    def register(cls, credential_type: str, credential: Type['CredentialModel']) -> None:
        cls._credentials[credential_type] = credential

    @classmethod
    def get(cls, credential_type: str) -> Optional[CredentialModel]:
        from app.credential.credentials.whatsapp_credential import WhatsAppCredential
        return cls._credentials[credential_type]()

    @classmethod
    def get_all_credentials(cls) -> List[Any]:
        from app.credential.credentials.whatsapp_credential import WhatsAppCredential
        return [credential().dict() for credential in cls._credentials.values()]

def credential_provider(name:str) -> Callable:

    def decorator(cls: Type[CredentialModel]) -> Type[CredentialModel]:
        CredentialRegistry.register(name, cls)
        return cls
    return decorator