"""
MongoDB repository for event management.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import ASCENDING, IndexModel
from pymongo.errors import Duplicate<PERSON>eyError

from app.schemas.event_type import EventCreate, EventStatus
from app.utils.exceptions import ConflictError, NotFoundError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger("repositories.event")


class EventRepository:
    """Repository for event operations in MongoDB."""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize event repository.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self.collection = db.events
        
    async def ensure_indexes(self) -> None:
        """Create indexes for the events collection."""
        indexes = [
            IndexModel([("event_type", ASCENDING)], name="idx_event_type"),
            IndexModel([("status", ASCENDING)], name="idx_status"),
            IndexModel([("created_at", ASCENDING)], name="idx_created_at"),
            IndexModel([("correlation_id", ASCENDING)], name="idx_correlation_id", sparse=True),
            IndexModel([("workflow_id", ASCENDING)], name="idx_workflow_id", sparse=True),
        ]
        
        try:
            await self.collection.create_indexes(indexes)
            logger.info("Event collection indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create event indexes: {e}")
            raise DatabaseError(f"Failed to create event indexes: {e}")
    
    async def create(self, event_data: EventCreate) -> Dict[str, Any]:
        """
        Create a new event.
        
        Args:
            event_data: Event creation data
            
        Returns:
            Dict[str, Any]: Created event document
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            now = datetime.now(timezone.utc)
            
            document = {
                "event_type": event_data.event_type,
                "data": event_data.data,
                "source": event_data.source,
                "correlation_id": event_data.correlation_id,
                "status": EventStatus.IN_PROGRESS,
                "workflow_id": None,
                "validation_errors": None,
                "created_at": now,
                "updated_at": now,
            }
            
            result = await self.collection.insert_one(document)
            
            # Retrieve the created document
            created_doc = await self.collection.find_one({"_id": result.inserted_id})
            if not created_doc:
                raise DatabaseError("Failed to retrieve created event")
            
            # Convert ObjectId to string
            created_doc["id"] = str(created_doc["_id"])
            del created_doc["_id"]
            
            logger.info(f"Created event: {event_data.event_type} with ID: {created_doc['id']}")
            return created_doc
            
        except Exception as e:
            logger.error(f"Failed to create event: {e}")
            raise DatabaseError(f"Failed to create event: {e}")
    
    async def get_by_id(self, event_id: str) -> Optional[Dict[str, Any]]:
        """
        Get event by ID.
        
        Args:
            event_id: Event ID (ObjectId as string)
            
        Returns:
            Optional[Dict[str, Any]]: Event document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(event_id):
                return None
            
            document = await self.collection.find_one({"_id": ObjectId(event_id)})
            if document:
                document["id"] = str(document["_id"])
                del document["_id"]
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to get event by ID {event_id}: {e}")
            raise DatabaseError(f"Failed to get event: {e}")
    
    async def update_status(
        self, 
        event_id: str, 
        status: str, 
        workflow_id: Optional[str] = None,
        validation_errors: Optional[List[str]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Update event status and related fields.
        
        Args:
            event_id: Event ID (ObjectId as string)
            status: New status
            workflow_id: Optional workflow ID
            validation_errors: Optional validation error messages
            
        Returns:
            Optional[Dict[str, Any]]: Updated event document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(event_id):
                return None
            
            update_data = {
                "status": status,
                "updated_at": datetime.now(timezone.utc)
            }
            
            if workflow_id is not None:
                update_data["workflow_id"] = workflow_id
            
            if validation_errors is not None:
                update_data["validation_errors"] = validation_errors
            
            result = await self.collection.find_one_and_update(
                {"_id": ObjectId(event_id)},
                {"$set": update_data},
                return_document=True
            )
            
            if result:
                result["id"] = str(result["_id"])
                del result["_id"]
                logger.info(f"Updated event {event_id} status to {status}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to update event status: {e}")
            raise DatabaseError(f"Failed to update event status: {e}")
    
    async def get_by_correlation_id(self, correlation_id: str) -> List[Dict[str, Any]]:
        """
        Get events by correlation ID.
        
        Args:
            correlation_id: Correlation ID
            
        Returns:
            List[Dict[str, Any]]: List of event documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            cursor = self.collection.find({"correlation_id": correlation_id})
            documents = await cursor.to_list(length=None)
            
            # Convert ObjectId to string for each document
            for doc in documents:
                doc["id"] = str(doc["_id"])
                del doc["_id"]
            
            return documents
            
        except Exception as e:
            logger.error(f"Failed to get events by correlation ID: {e}")
            raise DatabaseError(f"Failed to get events by correlation ID: {e}")
    
    async def get_by_status(
        self, 
        status: str, 
        limit: int = 100, 
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get events by status with pagination.
        
        Args:
            status: Event status
            limit: Maximum number of events to return
            skip: Number of events to skip
            
        Returns:
            List[Dict[str, Any]]: List of event documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            cursor = self.collection.find({"status": status}).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string for each document
            for doc in documents:
                doc["id"] = str(doc["_id"])
                del doc["_id"]
            
            return documents
            
        except Exception as e:
            logger.error(f"Failed to get events by status: {e}")
            raise DatabaseError(f"Failed to get events by status: {e}")
