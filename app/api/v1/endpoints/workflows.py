"""
WorkFlow API endpoints for workflow management.
"""

import uuid
from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.common import PaginationParams, BaseResponse
from app.schemas.workflow import (
    Work<PERSON>lowCreate,
    WorkFlow as WorkFlowSchema,
    WorkFlowFlattenedResponse,
    SimplifiedTag,
    WorkFlowList
)
from app.services.workflow_service import WorkFlowService
from app.services.temporal_service import TemporalService
from app.utils.exceptions import (
    NotFoundError,
    ValidationError,
    ConflictError,
    ExternalServiceError
)
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.workflows")


@router.post("/", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_201_CREATED)
async def create_workflow(
    workflow_data: WorkFlowCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Create a new workflow with its initial version.

    This endpoint creates both a WorkFlow and its initial WorkFlowVersion in a single transaction.
    The workflow metadata and version-specific data are handled through the work_flow field
    in the WorkFlowCreate schema.

    The work_flow field supports:
    - Creating new versions (when work_flow_id is not provided)
    - Updating existing versions (when work_flow_id is provided)

    Args:
        workflow_data: Workflow creation data including metadata, tags, and optional work_flow definition
        current_user: Current authenticated user
        db: Database session

    Returns:
        BaseResponse[WorkFlowFlattenedResponse]: Standardized response with flattened workflow data

    Raises:
        ConflictError: If workflow name already exists
        ValidationError: If tag IDs are invalid or work_flow_id doesn't exist/belong to workflow
    """
    try:
        # Set created_by and edited_by from current user
        workflow_data.created_by = current_user.id
        workflow_data.edited_by = current_user.id
        
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.create_workflow(
            workflow_data
        )
        
        logger.info(
            "Workflow created via API",
            workflow_id=workflow.uid,
            workflow_name=workflow.name,
            created_by=current_user.id
        )

        # Convert to flattened response format
        return workflow_service.to_flattened_response(workflow)
    
    except ConflictError as e:
        logger.warning(f"Workflow creation conflict: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow creation validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating workflow: {str(e)}")
        raise


@router.get("/{workflow_id}", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_200_OK)
async def get_workflow(
    workflow_id: uuid.UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Get a workflow by ID with flattened structure combining workflow and active version data.

    This endpoint retrieves a workflow with a flattened response structure that combines:
    - Workflow metadata (uid, name, is_active, created_by, etc.)
    - Active version data (id, version_no, work_flow definition)
    - Associated tags
    - Complete workflow definition with nodes and connections

    Args:
        workflow_id: Workflow UID
        current_user: Current authenticated user
        db: Database session

    Returns:
        BaseResponse[WorkFlowFlattenedResponse]: Standardized response with flattened workflow data

    Raises:
        NotFoundError: If workflow not found or has no active version
    """
    try:
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.get_workflow(workflow_id)

        # Find the active version
        active_version = None
        if workflow.active_version_id:
            active_version = next(
                (v for v in workflow.versions if v.id == workflow.active_version_id),
                None
            )

        if not active_version:
            raise NotFoundError(f"Workflow {workflow_id} has no active version")

        logger.info(
            "Workflow retrieved via API",
            workflow_id=workflow_id,
            active_version_id=active_version.id,
            requested_by=current_user.id
        )

        # Create simplified tags (only id and name)
        simplified_tags = [
            SimplifiedTag(id=tag.id, name=tag.name)
            for tag in workflow.tags
        ]

        # Create flattened response combining workflow and active version data
        flattened_data = WorkFlowFlattenedResponse(
            # Workflow fields
            created_at=workflow.created_at.isoformat() + "Z",
            updated_at=workflow.updated_at.isoformat() + "Z",
            name=workflow.name,
            is_active=workflow.is_active,
            description=workflow.description,
            status=workflow.status.value,
            created_by=workflow.created_by,
            edited_by=workflow.edited_by,
            uid=str(workflow.uid),
            tags=simplified_tags,

            # Active version fields (renamed id to active_version_id)
            active_version_id=active_version.id,
            version_no=active_version.version_no,
            version_name=active_version.version_name,
            work_flow=active_version.work_flow
        )

        return flattened_data 
               
    except NotFoundError as e:
        logger.warning(f"Workflow not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving workflow {workflow_id}: {str(e)}")
        raise


@router.get("/", response_model=WorkFlowList, status_code=status.HTTP_200_OK)
async def get_workflows(
    pagination: PaginationParams = Depends(),
    active_only: bool = Query(True, description="Filter to active workflows only"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    tag_ids: Optional[str] = Query(None, description="Comma-separated tag IDs to filter by"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowList:
    """
    Get paginated list of workflows with filtering options.
    
    This endpoint supports:
    - Pagination with skip/limit
    - Filtering by active status
    - Filtering by creator
    - Filtering by associated tags
    
    Args:
        pagination: Pagination parameters (skip, limit)
        active_only: Whether to return only active workflows
        created_by: Filter by creator user ID
        tag_ids: Comma-separated tag IDs to filter by
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowList: Paginated workflow list with metadata
    """
    try:
        # Parse tag IDs if provided
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if tag_ids:
            try:
                tag_id_list = [int(tag_id.strip()) for tag_id in tag_ids.split(",")]
                filters["tag_ids"] = tag_id_list
            except ValueError:
                raise ValidationError("Invalid tag IDs format. Use comma-separated integers.")
        
        workflow_service = WorkFlowService(db)
        workflow_list = await workflow_service.get_workflows_paginated(
            skip=pagination.skip,
            limit=pagination.limit,
            filters=filters,
            active_only=active_only
        )
        
        logger.info(
            "Workflows list retrieved via API",
            total_count=workflow_list.total,
            page=workflow_list.page,
            requested_by=current_user.id
        )
        
        return workflow_list
        
    except ValidationError as e:
        logger.warning(f"Workflow list validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving workflows: {str(e)}")
        raise


@router.put("/{workflow_id}", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_200_OK)
async def update_workflow(
    workflow_id: uuid.UUID,
    workflow_data: WorkFlowCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Update an existing workflow with the same structure as creation.

    This endpoint accepts the exact JSON structure as the POST endpoint and updates
    both the parent workflow fields (name, description, is_active, status) and
    associated version fields in a single request.

    The request body follows the established schema with:
    - Top-level properties: name, description, is_active, status, tag_ids
    - Nested work_flow object containing work_flow_version_id, start_node, nodes, connections

    Args:
        workflow_id: Workflow UID to update
        workflow_data: Complete workflow data including metadata, tags, and work_flow definition
        current_user: Current authenticated user
        db: Database session

    Returns:
        BaseResponse[WorkFlowFlattenedResponse]: Standardized response with flattened workflow data

    Raises:
        NotFoundError: If workflow not found
        ValidationError: If update data is invalid or tag IDs don't exist
    """
    try:
        # Validate that workflow exists first (early validation)
        workflow_service = WorkFlowService(db)
        existing_workflow = await workflow_service.get_workflow(workflow_id)
        if not existing_workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")

        # Set edited_by from current user
        workflow_data.edited_by = current_user.id

        # Perform early validation before database operations
        if workflow_data.tag_ids:
            await workflow_service._validate_tag_ids(workflow_data.tag_ids)

        # Validate node types if workflow definition is provided
        if workflow_data.work_flow and workflow_data.work_flow.nodes:
            workflow_service._validate_node_types(workflow_data.work_flow.nodes)
        print(f"Workflow data: {workflow_data.model_dump()}")
        # Update the workflow using the unified service method
        workflow = await workflow_service.update_workflow_unified(
            workflow_id=workflow_id,
            workflow_data=workflow_data
        )

        logger.info(
            "Workflow updated via API",
            workflow_id=workflow_id,
            workflow_name=workflow.name,
            updated_by=current_user.id
        )

        # Convert to flattened response format
        return workflow_service.to_flattened_response(workflow) 
        
    except NotFoundError as e:
        logger.warning(f"Workflow update failed - not found: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow update validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating workflow {workflow_id}: {str(e)}")
        raise


@router.patch("/{workflow_id}/deactivate", response_model=WorkFlowSchema, status_code=status.HTTP_200_OK)
async def deactivate_workflow(
    workflow_id: uuid.UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowSchema:
    """
    Deactivate a workflow.
    
    This endpoint sets the workflow's is_active flag to False,
    effectively removing it from active workflow lists.
    
    Args:
        workflow_id: Workflow UID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowSchema: Deactivated workflow
        
    Raises:
        NotFoundError: If workflow not found
    """
    try:
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.deactivate_workflow(workflow_id)
        
        logger.info(
            "Workflow deactivated via API",
            workflow_id=workflow_id,
            deactivated_by=current_user.id
        )
        
        return WorkFlowSchema.model_validate(workflow)
        
    except NotFoundError as e:
        logger.warning(f"Workflow deactivation failed - not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error deactivating workflow {workflow_id}: {str(e)}")
        raise


@router.post("/test-event", response_model=Dict[str, Any], status_code=status.HTTP_200_OK)
async def test_workflow_event(
    workflow_data: WorkFlowCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Test/execute a workflow event without persisting to database.

    This endpoint accepts the same JSON schema structure as the POST /api/v1/workflows/ endpoint
    and executes the workflow using the test_worker for testing purposes. The workflow is not
    saved to the database.

    The request body follows the established schema with:
    - Top-level properties: name, description, is_active, status, tag_ids
    - Nested work_flow object containing: work_flow_id, start_node, nodes, connections

    Args:
        workflow_data: Complete workflow data including metadata and work_flow definition
        current_user: Current authenticated user
        db: Database session

    Returns:
        BaseResponse[Dict[str, Any]]: Standardized response with workflow execution results

    Raises:
        ValidationError: If workflow definition is invalid or node types are not registered
        ExternalServiceError: If Temporal service is not available or workflow execution fails
    """
    try:
        # Set user context for validation
        workflow_data.created_by = current_user.id
        workflow_data.edited_by = current_user.id

        workflow_service = WorkFlowService(db)

        # Validate tag IDs if provided (same validation as create endpoint)
        if workflow_data.tag_ids:
            await workflow_service._validate_tag_ids(workflow_data.tag_ids)

        # Validate node types if workflow definition is provided (same validation as create endpoint)
        if workflow_data.work_flow and workflow_data.work_flow.nodes:
            workflow_service._validate_node_types(workflow_data.work_flow.nodes)

        # Convert WorkFlowCreate to WorkflowModel for execution
        workflow_model = await workflow_service.convert_to_workflow_model(workflow_data)

        # Execute workflow using test worker
        temporal_service = TemporalService()
        if not temporal_service.is_connected():
            await temporal_service.connect()

        # Start test workflow execution
        workflow_handle = await temporal_service.start_workflow(
            "test_event_workflow",
            workflow_model,
            id=f"test-workflow-{uuid.uuid4()}",
            task_queue=temporal_service.test_task_queue
        )

        # Wait for workflow completion and get result
        # execution_result = await workflow_handle.result()

        logger.info(
            "Test workflow executed successfully",
            workflow_name=workflow_data.name,
            executed_by=current_user.id,
            execution_status="started",
            workflow_id=workflow_handle.id,
            workflow_run_id=workflow_handle.result_run_id
        )

        return {
            "status": "started",
            "workflow_id": workflow_handle.id,
            "run_id": workflow_handle.result_run_id
        }

    except ValidationError as e:
        logger.warning(f"Workflow test validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error testing workflow: {str(e)}")
        raise ExternalServiceError(
            message=f"Failed to execute test workflow: {str(e)}",
            service_name="temporal"
        )


@router.get("/execution/{workflow_execution_id}", response_model=Dict[str, Any], status_code=status.HTTP_200_OK)
async def get_workflow_execution_status(
    workflow_execution_id: str,
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """
    Get the status of a workflow execution by its ID.

    Args:
        workflow_execution_id: The ID of the workflow execution to check
        current_user: Current authenticated user

    Returns:
        Dict[str, Any]: Status information about the workflow execution

    Raises:
        NotFoundError: If workflow execution not found
        ExternalServiceError: If Temporal service is not available
    """
    try:
        temporal_service = TemporalService()
        if not temporal_service.is_connected():
            await temporal_service.connect()

        return await temporal_service.get_workflow_result(workflow_execution_id)

    except Exception as e:
        logger.error(f"Error getting workflow execution status: {str(e)}")
        raise ExternalServiceError(
            message=f"Failed to get workflow execution status: {str(e)}",
            service_name="temporal"
        )