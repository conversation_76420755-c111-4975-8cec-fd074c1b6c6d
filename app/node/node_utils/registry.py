"""
Node Registry

This module provides a registry for all node types available in the application.
It allows for node discovery, registration, and searching.
"""

from typing import Dict, List, Optional, Type, Any
import re
from fuzzywuzzy import fuzz, process

from typing import Dict, Any, Type

from typing import Dict, Any, Type

from app.node.node_base.node import Node
from app.node.node_base.node_models import NodeTypeDescription

class NodeRegistry:
    """    A singleton registry for managing node types in the application.
    This class allows for registering, retrieving, and searching nodes by type.
    It supports fuzzy searching to handle typos and similar terms.
    """

    def __init__(self):
        self._registry = {}

    # @classmethod
    def register_node(self, node_type: str, node_class: Type[Node]) -> None:
        """
        Register a node class with its node type in the registry.
        
        Args:
            node_type (str): The type of the node (e.g., 'if', 'delay').
            node_class (type): The class of the node.
        """
        self._registry[node_type] = node_class
        
    def get_node_class(self, node_type: str) -> Optional[Type[Node]]:
        """
        Get a node class by its type.
        
        Args:
            node_type: The node type identifier
            
        Returns:
            The node class if found, None otherwise
        """
        return self._registry.get(node_type, None)
        
    def get_activity_node_class(self) -> List[Type[Node]]:
        nodes = []
        for node_class in self._registry.values():
            if hasattr(node_class, 'is_activity') and node_class.is_activity:
                nodes.append(node_class)
        return nodes

    def list_node_descriptions(self) -> List[NodeTypeDescription]:
        """
        Get a list of all node type descriptions.
        
        Returns:
            List of node type descriptions (preserving all properties from child classes)
        """
        descriptions = []
        
        for node_class in self._registry.values():
            # Get the description from the node class
            # This will include all properties from child classes
            description = node_class.get_description()
            descriptions.append(description)
        
        return descriptions
    
    def get(self, node_type: str) -> Optional[Node]:
        """
        Get a node description by its type.
        
        Args:
            node_type: The node type identifier
            
        Returns:
            The node description if found, None otherwise
        """
        node_class = self.get_node_class(node_type)
        if not node_class:
            return None
        return node_class()


# Create a singleton instance
node_registry = NodeRegistry()
    
    
# Export the registry instance as the public API
__all__ = ['node_registry']
