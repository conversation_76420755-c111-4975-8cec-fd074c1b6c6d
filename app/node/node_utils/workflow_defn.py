import asyncio
from typing import Type, Callable

from app.node.node_utils.registry import node_registry
from temporalio import workflow, activity

from app.node.node_base.node import Node



def node_defn(type: str, is_activity: bool = True) -> Callable[[Type[Node]], Type[Node]]:
    """
    Custom decorator to apply @workflow.defn on a class that represents a node.
    
    This allows the class to be used as a workflow definition in Temporal.
    """
    
    def decorator(cls: Type[Node]):
        setattr(cls, 'is_activity', is_activity)

        # Apply @workflow.run to the 'run' method if it exists
        for name, method in cls.__dict__.items():
            if callable(method) and asyncio.iscoroutinefunction(method) and name == "run":
                if is_activity:
                    # If it's an activity, apply @activity.defn
                    method = activity.defn(method)
                else:
                    # If it's a workflow, apply @workflow.run
                    method = workflow.run(method)

        # Register node in the registry
        node_registry.register_node(type, cls)

        # if not is_activity:
        #     cls = workflow.defn(cls)

        return cls
    
    return decorator