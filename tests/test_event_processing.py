"""
Test cases for the event processing pipeline.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from app.services.event_handler import process_event, _extract_event_data
from app.schemas.event_type import EventCreate, EventStatus


class TestEventProcessing:
    """Test cases for event processing functionality."""
    
    @pytest.mark.asyncio
    async def test_extract_event_data_direct_structure(self):
        """Test extracting event data from direct event structure."""
        event = {
            "body": '{"event_type": "user_signup", "data": {"user_id": 123, "email": "<EMAIL>"}}',
            "source": "lambda",
            "requestId": "test-request-123"
        }
        
        result = await _extract_event_data(event)
        
        assert result is not None
        assert result.event_type == "user_signup"
        assert result.data == {"user_id": 123, "email": "<EMAIL>"}
        assert result.source == "lambda"
        assert result.correlation_id == "test-request-123"
    
    @pytest.mark.asyncio
    async def test_extract_event_data_lambda_structure(self):
        """Test extracting event data from Lambda event structure."""
        event = {
            "body": '{"eventType": "order_created", "data": {"order_id": 456, "amount": 99.99}}',
            "source": "api_gateway"
        }
        
        result = await _extract_event_data(event)
        
        assert result is not None
        assert result.event_type == "order_created"
        assert result.data == {"order_id": 456, "amount": 99.99}
        assert result.source == "api_gateway"
        assert result.correlation_id is not None  # Should generate UUID
    
    @pytest.mark.asyncio
    async def test_extract_event_data_generic_message(self):
        """Test extracting event data from generic message structure."""
        event = {
            "body": '{"message": "Hello World", "timestamp": "2023-01-01T00:00:00Z"}',
            "source": "webhook"
        }
        
        result = await _extract_event_data(event)
        
        assert result is not None
        assert result.event_type == "generic_message"
        assert result.data == {"message": "Hello World", "timestamp": "2023-01-01T00:00:00Z"}
        assert result.source == "webhook"
    
    @pytest.mark.asyncio
    async def test_extract_event_data_plain_text(self):
        """Test extracting event data from plain text body."""
        event = {
            "body": "Simple text message",
            "source": "sms"
        }
        
        result = await _extract_event_data(event)
        
        assert result is not None
        assert result.event_type == "generic_message"
        assert result.data == {"message": "Simple text message"}
        assert result.source == "sms"
    
    @pytest.mark.asyncio
    async def test_extract_event_data_invalid_json(self):
        """Test extracting event data from invalid JSON."""
        event = {
            "body": '{"invalid": json}',
            "source": "test"
        }
        
        result = await _extract_event_data(event)
        
        assert result is not None
        assert result.event_type == "generic_message"
        assert result.data == {"message": '{"invalid": json}'}
        assert result.source == "test"
    
    @pytest.mark.asyncio
    @patch('app.services.event_handler.get_mongodb')
    @patch('app.services.event_handler.TemporalService')
    @patch('app.services.event_handler.EventService')
    async def test_process_event_success_flow(self, mock_event_service_class, mock_temporal_service_class, mock_get_mongodb):
        """Test successful event processing flow."""
        # Setup mocks
        mock_mongodb = AsyncMock()
        mock_get_mongodb.return_value = mock_mongodb
        
        mock_temporal_service = AsyncMock()
        mock_temporal_service_class.return_value = mock_temporal_service
        
        mock_event_service = AsyncMock()
        mock_event_service_class.return_value = mock_event_service
        
        # Mock event service methods
        created_event = {
            "id": "event-123",
            "event_type": "test_event",
            "status": EventStatus.IN_PROGRESS,
            "data": {"test": "data"}
        }
        mock_event_service.create_event.return_value = created_event
        
        validated_event = created_event.copy()
        validated_event["status"] = EventStatus.IN_PROGRESS
        mock_event_service.validate_event_data.return_value = validated_event
        
        final_event = validated_event.copy()
        final_event["status"] = EventStatus.WORKFLOW_STARTED
        final_event["workflow_id"] = "workflow-456"
        mock_event_service.trigger_workflow.return_value = final_event
        
        # Test event
        event = {
            "body": '{"event_type": "test_event", "data": {"test": "data"}}',
            "source": "test"
        }
        
        # Execute
        await process_event(event)
        
        # Verify calls
        mock_event_service.create_event.assert_called_once()
        mock_event_service.validate_event_data.assert_called_once_with("event-123")
        mock_event_service.trigger_workflow.assert_called_once_with("event-123")
    
    @pytest.mark.asyncio
    @patch('app.services.event_handler.get_mongodb')
    @patch('app.services.event_handler.TemporalService')
    @patch('app.services.event_handler.EventService')
    async def test_process_event_validation_failure(self, mock_event_service_class, mock_temporal_service_class, mock_get_mongodb):
        """Test event processing with validation failure."""
        # Setup mocks
        mock_mongodb = AsyncMock()
        mock_get_mongodb.return_value = mock_mongodb
        
        mock_temporal_service = AsyncMock()
        mock_temporal_service_class.return_value = mock_temporal_service
        
        mock_event_service = AsyncMock()
        mock_event_service_class.return_value = mock_event_service
        
        # Mock event service methods
        created_event = {
            "id": "event-123",
            "event_type": "test_event",
            "status": EventStatus.IN_PROGRESS,
            "data": {"invalid": "data"}
        }
        mock_event_service.create_event.return_value = created_event
        
        # Validation fails
        failed_event = created_event.copy()
        failed_event["status"] = EventStatus.VALIDATION_FAILED
        failed_event["validation_errors"] = ["Field 'required_field' is missing"]
        mock_event_service.validate_event_data.return_value = failed_event
        
        # Test event
        event = {
            "body": '{"event_type": "test_event", "data": {"invalid": "data"}}',
            "source": "test"
        }
        
        # Execute
        await process_event(event)
        
        # Verify calls
        mock_event_service.create_event.assert_called_once()
        mock_event_service.validate_event_data.assert_called_once_with("event-123")
        # Workflow should not be triggered for failed validation
        mock_event_service.trigger_workflow.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_extract_event_data_error_handling(self):
        """Test error handling in event data extraction."""
        # Test with None event
        result = await _extract_event_data(None)
        assert result is None
        
        # Test with empty event
        result = await _extract_event_data({})
        assert result is not None
        assert result.event_type == "generic_event"
        assert result.data == {}


if __name__ == "__main__":
    pytest.main([__file__])
