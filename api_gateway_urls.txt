# API Gateway URLs (proxied through LocalStack)
API Gateway Base URL: http://localhost:4566/restapis/qauzvkachl/dev/_user_request_
Backend API URL: http://localhost:4566/restapis/qauzvkachl/dev/_user_request_/backend
Event Endpoint URL: http://localhost:4566/restapis/qauzvkachl/dev/_user_request_/event

# Direct URLs (bypass API Gateway)
FastAPI Direct URL: http://localhost:8008
API Documentation: http://localhost:8008/api/v1/docs
OpenAPI Schema: http://localhost:8008/api/v1/openapi.json

# IMPORTANT NOTES:
# 1. When accessing the API docs via API Gateway (http://localhost:4566/restapis/qauzvkachl/dev/_user_request_/backend/api/v1/docs),
#    you may see "Fetch error NOT FOUND /api/v1/openapi.json".
# 2. This is because API Gateway doesn't correctly proxy the OpenAPI schema request.
# 3. For API documentation, always use the direct FastAPI URL: http://localhost:8008/api/v1/docs
