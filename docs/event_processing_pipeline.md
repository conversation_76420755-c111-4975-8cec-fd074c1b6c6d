# Event Processing Pipeline Implementation

## Overview

This document describes the complete event processing pipeline implementation for the Cerebro project. The pipeline handles incoming events from AWS Lambda via RabbitMQ and processes them through validation and workflow execution stages.

## Architecture

The event processing pipeline consists of the following components:

### 1. Event Handler (`app/services/event_handler.py`)
- **Main Entry Point**: `process_event()` function
- **Responsibilities**: 
  - Extract event data from incoming payloads
  - Orchestrate the complete processing pipeline
  - Handle errors and logging

### 2. Event Service (`app/services/event_service.py`)
- **Business Logic Layer**: Manages event processing operations
- **Key Methods**:
  - `create_event()`: Store events in MongoDB
  - `validate_event_data()`: Validate against JSON schemas
  - `trigger_workflow()`: Start workflow execution

### 3. Event Repository (`app/repositories/event_repository.py`)
- **Data Access Layer**: MongoDB operations for events
- **Key Methods**:
  - `create()`: Insert new events
  - `update_status()`: Update event processing status
  - `get_by_id()`: Retrieve events by ID

### 4. Event Schemas (`app/schemas/event_type.py`)
- **Data Models**: Pydantic schemas for type safety
- **Key Classes**:
  - `EventCreate`: For creating new events
  - `Event`: Complete event representation
  - `EventStatus`: Status constants

## Processing Pipeline

The `process_event()` function implements a 3-step pipeline:

### Step 1: Event Storage
```python
# Extract event data from payload
event_data = await _extract_event_data(event)

# Store in MongoDB with "in_progress" status
created_event = await event_service.create_event(event_data)
```

**What happens:**
- Parses incoming event payload (JSON, plain text, etc.)
- Extracts event type and data
- Stores in MongoDB `events` collection
- Sets initial status to `"in_progress"`

### Step 2: Schema Validation
```python
# Validate against event type schema
validated_event = await event_service.validate_event_data(event_id)
```

**What happens:**
- Retrieves event type configuration from `event_types` collection
- Gets associated JSON schema
- Validates event data against schema using `jsonschema` library
- Updates status to `"validation_failed"` if validation fails
- Continues to workflow execution if validation succeeds

### Step 3: Workflow Execution
```python
# Trigger workflow execution
final_event = await event_service.trigger_workflow(event_id)
```

**What happens:**
- Retrieves workflow IDs associated with the event type
- Converts workflow to `WorkflowModel` format
- Starts workflow execution using Temporal
- Updates status to `"workflow_started"` on success

## Event Status Flow

Events progress through the following statuses:

1. `"in_progress"` - Initial status when event is stored
2. `"validation_failed"` - Schema validation failed
3. `"workflow_started"` - Workflow execution initiated
4. `"completed"` - Processing completed (no workflow to execute)
5. `"failed"` - Error occurred during processing

## Data Structures

### Event Document (MongoDB)
```json
{
  "_id": "ObjectId",
  "event_type": "user_signup",
  "data": {
    "user_id": 123,
    "email": "<EMAIL>"
  },
  "source": "lambda",
  "correlation_id": "uuid-string",
  "status": "workflow_started",
  "workflow_id": "workflow-uuid",
  "validation_errors": null,
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}
```

### Event Type Configuration
```json
{
  "_id": "ObjectId",
  "name": "user_signup",
  "description": "User registration event",
  "schema": {
    "type": "object",
    "properties": {
      "user_id": {"type": "integer"},
      "email": {"type": "string", "format": "email"}
    },
    "required": ["user_id", "email"]
  },
  "workflows": ["workflow-uuid-1", "workflow-uuid-2"],
  "version": 1
}
```

## Error Handling

The pipeline includes comprehensive error handling:

### Validation Errors
- Schema validation failures are captured
- Error messages stored in `validation_errors` field
- Event status updated to `"validation_failed"`

### Workflow Execution Errors
- Temporal service connection issues
- Workflow not found errors
- General execution failures

### Database Errors
- MongoDB connection issues
- Document creation/update failures

## Configuration Requirements

### MongoDB Collections
- `events` - Stores event processing records
- `event_types` - Stores event type configurations and schemas

### Dependencies
- Event types must be configured with JSON schemas
- Workflows must be associated with event types
- Temporal service must be available for workflow execution

## Usage Examples

### Basic Event Processing
```python
# Incoming event from Lambda/RabbitMQ
event = {
    "body": '{"event_type": "user_signup", "data": {"user_id": 123, "email": "<EMAIL>"}}',
    "source": "api_gateway",
    "requestId": "req-123"
}

# Process the event
await process_event(event)
```

### Event Type Configuration
```python
# Create event type with schema
event_type = EventTypeCreate(
    name="user_signup",
    description="User registration event",
    schema=JSONSchema({
        "type": "object",
        "properties": {
            "user_id": {"type": "integer"},
            "email": {"type": "string", "format": "email"}
        },
        "required": ["user_id", "email"]
    }),
    workflows=["workflow-uuid"]
)
```

## Testing

The implementation includes comprehensive tests:

- Unit tests for event data extraction
- Integration tests for the complete pipeline
- Mock-based tests for service interactions
- Error handling test cases

Run tests with:
```bash
pytest tests/test_event_processing.py -v
```

## Future Enhancements

1. **Retry Mechanism**: Implement retry logic for failed events
2. **Dead Letter Queue**: Handle permanently failed events
3. **Event Routing**: Support multiple workflows per event type
4. **Monitoring**: Add metrics and health checks
5. **Batch Processing**: Support processing multiple events together
